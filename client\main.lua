local QBCore = exports['qb-core']:GetCoreObject()

local function GetPlayerGender()
    local ped = PlayerPedId()
    local model = GetEntityModel(ped)

    -- Check if the ped model is female
    if model == GetHashKey("mp_f_freemode_01") or
       string.find(string.lower(GetEntityArchetypeName(ped) or ""), "female") or
       string.find(string.lower(GetEntityArchetypeName(ped) or ""), "_f_") then
        return 'female'
    else
        return 'male'
    end
end

local function ShowNotification(message, type)
    if Config.Notifications.type == 'ox_lib' then
        lib.notify({
            title = 'Stress Relief',
            description = message,
            type = type or 'success',
            position = Config.Notifications.position
        })
    else
        QBCore.Functions.Notify(message, type or 'success')
    end
end

local function ShowProgressBar(data)
    local gender = GetPlayerGender()
    local animationData = data.animation and data.animation[gender] or data.animation

    if Config.ProgressBar.type == 'ox_lib' then
        return lib.progressBar({
            duration = data.duration,
            label = data.label,
            useWhileDead = data.useWhileDead,
            canCancel = data.canCancel,
            disable = {
                car = data.disableCarMovement,
                move = data.disableMovement,
                mouse = data.disableMouse,
                combat = data.disableCombat
            },
            anim = animationData and {
                dict = animationData.animDict,
                clip = animationData.anim,
                flag = animationData.flags
            } or nil
        })
    else

        QBCore.Functions.Progressbar('cigarette_pack_use', data.label, data.duration, false, data.canCancel, {
            disableMovement = data.disableMovement,
            disableCarMovement = data.disableCarMovement,
            disableMouse = data.disableMouse,
            disableCombat = data.disableCombat
        }, animationData and {
            animDict = animationData.animDict,
            anim = animationData.anim,
            flags = animationData.flags
        } or nil, {}, {}, function()

        end, function()

        end)
        return true
    end
end

local function ReduceStress(amount)
    TriggerServerEvent('hud:server:RelieveStress', amount)
end

local function PlayAnimation(animationData, propData, duration)
    local ped = PlayerPedId()

    RequestAnimDict(animationData.dict)
    while not HasAnimDictLoaded(animationData.dict) do
        Wait(0)
    end

    RequestModel(propData.model)
    while not HasModelLoaded(propData.model) do
        Wait(0)
    end

    local prop = CreateObject(propData.model, 0, 0, 0, true, true, true)
    AttachEntityToEntity(prop, ped, GetPedBoneIndex(ped, propData.bone),
        propData.coords.x, propData.coords.y, propData.coords.z,
        propData.rotation.x, propData.rotation.y, propData.rotation.z,
        true, true, false, true, 1, true)

    TaskPlayAnim(ped, animationData.dict, animationData.name, 8.0, -8.0, duration, animationData.flag, 0, false, false, false)

    Wait(duration)

    DeleteObject(prop)
    RemoveAnimDict(animationData.dict)
    SetModelAsNoLongerNeeded(propData.model)
end

local function PlaySmokingAnimation(cigaretteData)
    local ped = PlayerPedId()
    local gender = GetPlayerGender()
    local scenario = cigaretteData.scenarios[gender] or cigaretteData.scenarios.male

    TaskStartScenarioInPlace(ped, scenario, 0, true)

    if cigaretteData.smoke.enabled then
        local smokingActive = true
        CreateThread(function()
            while smokingActive do
                local pedCoords = GetEntityCoords(ped)
                local pedHeading = GetEntityHeading(ped)

                local forwardX = -math.sin(math.rad(pedHeading)) * 0.1
                local forwardY = math.cos(math.rad(pedHeading)) * 0.1
                local cigarettePos = vector3(
                    pedCoords.x + forwardX,
                    pedCoords.y + forwardY,
                    pedCoords.z + 1.5
                )

                StartParticleFxNonLoopedAtCoord(
                    cigaretteData.smoke.particleAsset,
                    cigaretteData.smoke.particleEffect,
                    cigarettePos.x, cigarettePos.y, cigarettePos.z,
                    0.0, 0.0, 0.0,
                    cigaretteData.smoke.cigaretteScale,
                    false, false, false
                )

                if math.random(1, 100) <= cigaretteData.smoke.mouthChance then
                    local mouthX = -math.sin(math.rad(pedHeading)) * 0.15
                    local mouthY = math.cos(math.rad(pedHeading)) * 0.15

                    StartParticleFxNonLoopedAtCoord(
                        cigaretteData.smoke.particleAsset,
                        cigaretteData.smoke.particleEffect,
                        pedCoords.x + mouthX, pedCoords.y + mouthY, pedCoords.z + 1.6,
                        0.0, 0.0, 0.0,
                        cigaretteData.smoke.mouthScale,
                        false, false, false
                    )
                end

                Wait(cigaretteData.smoke.cigaretteInterval)
            end
        end)

        Wait(cigaretteData.duration)

        smokingActive = false
    else

        Wait(cigaretteData.duration)
    end

    ClearPedTasks(ped)
end

RegisterNetEvent('infc_stressrelief:useCoffee', function()
    local coffeeData = Config.Items.coffee

    PlayAnimation(coffeeData.animation, coffeeData.prop, coffeeData.duration)
    ReduceStress(coffeeData.stressRelief)
    ShowNotification(Config.Messages.coffee_consumed, 'success')
end)

RegisterNetEvent('infc_stressrelief:startSmoking', function()
    local cigaretteData = Config.Items.cigarette

    PlaySmokingAnimation(cigaretteData)
    ReduceStress(cigaretteData.stressRelief)
    ShowNotification(Config.Messages.cigarette_smoked, 'success')
end)

local function UseCigarettePack()
    local ped = PlayerPedId()

    if IsPedInAnyVehicle(ped, false) then
        ShowNotification(Config.Messages.no_smoking_in_vehicle, 'error')
        return
    end

    local packData = Config.Items.cigarette_pack

    if packData.progressBar.enabled then
        local success = ShowProgressBar(packData.progressBar)

        if Config.ProgressBar.type == 'ox_lib' then

            if success then
                TriggerServerEvent('infc_stressrelief:useCigarettePack')
            else
                ShowNotification('Cancelled taking cigarette', 'error')
            end
        else

            TriggerServerEvent('infc_stressrelief:useCigarettePack')
        end
    else

        TriggerServerEvent('infc_stressrelief:useCigarettePack')
    end
end

local function UseCigarette()
    local ped = PlayerPedId()

    if IsPedInAnyVehicle(ped, false) then
        ShowNotification(Config.Messages.no_smoking_in_vehicle, 'error')
        return
    end

    TriggerServerEvent('infc_stressrelief:useCigarette')
end

exports('useCigarettePack', UseCigarettePack)
exports('useCigarette', UseCigarette)

RegisterCommand(Config.Commands.check_stress.name, function()
    local Player = QBCore.Functions.GetPlayerData()
    if Player then
        QBCore.Functions.TriggerCallback('infc_stressrelief:getStress', function(stress)
            ShowNotification(string.format(Config.Messages.stress_level, stress), 'info')
        end)
    end
end, Config.Commands.check_stress.permission)
