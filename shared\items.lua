return {
    ['coffee'] = {
        label = 'Coffee',
        weight = 200,
        stack = true,
        close = true,
        description = 'Hot coffee to reduce stress',
        consume = 1,
        client = {
            status = { stress = -15 },
            anim = { dict = 'amb@world_human_drinking@coffee@male@idle_a', clip = 'idle_c', flag = 49 },
            prop = { model = 'p_amb_coffeecup_01', 
                    pos = vec3(0.0, 0.0, 0.0), 
                    rot = vec3(0.0, 0.0, 0.0),
                    bone = 28422 },
            usetime = 5000,
            notification = 'You drank the coffee and feel relaxed'
        }
    },
    
    ['cigarette_pack'] = {
        label = 'Cigarette Pack',
        weight = 100,
        stack = false,
        close = true,
        description = 'A pack containing cigarettes',
        consume = 0,
        client = {
            export = 'infc_stressrelief.useCigarettePack'
        }
    },
    
    ['cigarette'] = {
        label = 'Cigarette',
        weight = 10,
        stack = true,
        close = true,
        description = 'A cigarette to reduce stress (requires lighter)',
        consume = 1,
        client = {
            export = 'infc_stressrelief.useCigarette'
        }
    },
    
    ['lighter'] = {
        label = 'Lighter',
        weight = 50,
        stack = true,
        close = true,
        description = 'A lighter to light cigarettes',
        consume = 0
    },
    
    ['empty_cigarette_pack'] = {
        label = 'Empty Cigarette Pack',
        weight = 50,
        stack = true,
        close = true,
        description = 'An empty cigarette pack that can be disposed'
    }
}
