--  ██████╗ ███████╗██╗   ██╗    ██╗     █████╗ ██╗  ██╗███╗   ███╗███████╗██████╗ ████████╗██╗ ██████╗ ███████╗██████╗
--  ██╔══██╗██╔════╝██║   ██║    ██║    ██╔══██╗██║  ██║████╗ ████║██╔════╝██╔══██╗╚══██╔══╝██║██╔════╝ ██╔════╝██╔══██╗
--  ██║  ██║█████╗  ██║   ██║    ██║    ███████║███████║██╔████╔██║█████╗  ██║  ██║   ██║   ██║██║  ███╗█████╗  ██████╔╝
--  ██║  ██║██╔══╝  ╚██╗ ██╔╝    ██║    ██╔══██║██╔══██║██║╚██╔╝██║██╔══╝  ██║  ██║   ██║   ██║██║   ██║██╔══╝  ██╔══██╗
--  ██████╔╝███████╗ ╚████╔╝     ██║    ██║  ██║██║  ██║██║ ╚═╝ ██║███████╗██████╔╝   ██║   ██║╚██████╔╝███████╗██║  ██║
--  ╚═════╝ ╚══════╝  ╚═══╝      ╚═╝    ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝     ╚═╝╚══════╝╚═════╝    ╚═╝   ╚═╝ ╚═════╝ ╚══════╝╚═╝  ╚═╝

Config = {}

Config.Items = {
    -- ═══════════════════════════════════════════════════════════
    --                           COFFEE
    -- ═══════════════════════════════════════════════════════════
    coffee = {
        name = 'coffee',
        label = 'Coffee',
        stressRelief = 15,                                      
        animation = {
            dict = 'amb@world_human_drinking@coffee@male@idle_a',
            name = 'idle_c',
            flag = 49
        },
        prop = {
            model = 'p_amb_coffeecup_01',
            bone = 28422,                                       
            coords = { x = 0.0, y = 0.0, z = 0.0 },
            rotation = { x = 0.0, y = 0.0, z = 0.0 }
        },
        duration = 5000                                        
    },

    -- ═══════════════════════════════════════════════════════════
    --                       CIGARETTE PACK
    -- ═══════════════════════════════════════════════════════════
    cigarette_pack = {
        name = 'cigarette_pack',
        label = 'Cigarette Pack',
        maxCigarettes = 20,                                    
        giveItem = 'cigarette',                                
        progressBar = {
            enabled = true,                                     
            duration = 2000,                                   
            label = 'Taking cigarette...',                     
            useWhileDead = false,                               
            canCancel = true,                                  
            disableMovement = false,                         
            disableCarMovement = false,                        
            disableMouse = false,                             
            disableCombat = true,                              
            animation = {
                animDict = 'amb@world_human_stand_mobile@male@text@exit',                        
                anim = 'exit',                        
                flags = 49                                     
            }
        }
    },

    -- ═══════════════════════════════════════════════════════════
    --                         CIGARETTE
    -- ═══════════════════════════════════════════════════════════
    cigarette = {
        name = 'cigarette',
        label = 'Cigarette',
        stressRelief = 10,
        requiresLighter = true,
        scenarios = {
            male = 'WORLD_HUMAN_SMOKING',
            female = 'WORLD_HUMAN_SMOKING'
        },
        duration = 40000,
        smoke = {
            enabled = true,
            cigaretteInterval = 40000,
            mouthChance = 20,
            particleAsset = 'core',
            particleEffect = 'exp_grd_bzgas_smoke',
            cigaretteScale = 0.08,
            mouthScale = 0.12
        }
    },

    -- ═══════════════════════════════════════════════════════════
    --                          LIGHTER
    -- ═══════════════════════════════════════════════════════════
    lighter = {
        name = 'lighter',
        label = 'Lighter',
        required_for = { 'cigarette' }                         
    },

    -- ═══════════════════════════════════════════════════════════
    --                     EMPTY CIGARETTE PACK
    -- ═══════════════════════════════════════════════════════════
    empty_cigarette_pack = {
        name = 'empty_cigarette_pack',
        label = 'Empty Cigarette Pack',
        description = 'An empty cigarette pack that can be disposed'
    }
}

-- ═══════════════════════════════════════════════════════════════
--                         STRESS SYSTEM
-- ═══════════════════════════════════════════════════════════════

Config.Stress = {
    min = 0,                                                  
    max = 100,                                                
    default = 0                                               
}

-- ═══════════════════════════════════════════════════════════════
--                        NOTIFICATIONS
-- ═══════════════════════════════════════════════════════════════

Config.Notifications = {
    type = 'ox_lib',                                          
    position = 'top-right'                                     
}

-- ═══════════════════════════════════════════════════════════════
--                        PROGRESS BAR
-- ═══════════════════════════════════════════════════════════════

Config.ProgressBar = {
    type = 'ox_lib',                                           
    position = 'bottom'                                         
}

-- ═══════════════════════════════════════════════════════════════
--                          MESSAGES
-- ═══════════════════════════════════════════════════════════════

Config.Messages = {

    coffee_consumed = 'You drank the coffee and feel relaxed',
    cigarette_smoked = 'You smoked the cigarette and feel calm',
    cigarette_taken = 'You took a cigarette from the pack',
    pack_finished = 'You finished the cigarette pack',
    items_received = 'You received stress relief items',

    no_cigarette_pack = 'You don\'t have a cigarette pack',
    no_cigarette = 'You don\'t have a cigarette',
    no_lighter = 'You need a lighter to smoke',
    pack_empty = 'The pack is empty',
    no_smoking_in_vehicle = 'You cannot smoke inside a vehicle',

    cigarettes_left = '%d cigarettes left in the pack',
    stress_level = 'Your stress level: %d%%'
}

-- ═══════════════════════════════════════════════════════════════
--                         ADMIN COMMANDS
-- ═══════════════════════════════════════════════════════════════

Config.Commands = {
    give_items = {
        name = 'givestressitems',
        description = 'Give stress relief items (for testing)',
        permission = 'admin',
        items = {
            { name = 'coffee', amount = 5 },
            { name = 'lighter', amount = 1 },
            { name = 'cigarette_pack', amount = 2 }
        }
    },

    check_stress = {
        name = 'stress',
        description = 'Check your current stress level',
        permission = false                                      
    }
}
