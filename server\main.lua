local QBCore = exports['qb-core']:GetCoreObject()

QBCore.Functions.CreateCallback('infc_stressrelief:getStress', function(source, cb)
    local Player = QBCore.Functions.GetPlayer(source)
    if Player then
        local stress = Player.PlayerData.metadata.stress or 0
        cb(stress)
    else
        cb(0)
    end
end)

RegisterNetEvent('infc_stressrelief:useCigarettePack', function()
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)

    if not Player then return end

    local packItem = Player.Functions.GetItemByName(Config.Items.cigarette_pack.name)
    if not packItem then
        TriggerClientEvent('QBCore:Notify', src, Config.Messages.no_cigarette_pack, 'error')
        return
    end

    local metadata = packItem.info or {}
    local cigarettesLeft = metadata.cigarettes or Config.Items.cigarette_pack.maxCigarettes

    if cigarettesLeft <= 0 then
        TriggerClientEvent('QBCore:Notify', src, Config.Messages.pack_empty, 'error')
        return
    end

    cigarettesLeft = cigarettesLeft - 1

    if cigarettesLeft <= 0 then

        Player.Functions.RemoveItem(Config.Items.cigarette_pack.name, 1, packItem.slot)
        Player.Functions.AddItem(Config.Items.empty_cigarette_pack.name, 1)
        TriggerClientEvent('QBCore:Notify', src, Config.Messages.pack_finished, 'info')
    else

        local newMetadata = {
            cigarettes = cigarettesLeft,
            description = string.format('A pack containing %d cigarettes', cigarettesLeft)
        }

        Player.Functions.RemoveItem(Config.Items.cigarette_pack.name, 1, packItem.slot)
        Player.Functions.AddItem(Config.Items.cigarette_pack.name, 1, false, newMetadata)

        TriggerClientEvent('QBCore:Notify', src, string.format(Config.Messages.cigarettes_left, cigarettesLeft), 'info')
    end

    Player.Functions.AddItem(Config.Items.cigarette.name, 1)
    TriggerClientEvent('QBCore:Notify', src, Config.Messages.cigarette_taken, 'success')
end)

RegisterNetEvent('infc_stressrelief:useCigarette', function()
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)

    if not Player then return end

    local cigaretteItem = Player.Functions.GetItemByName(Config.Items.cigarette.name)
    if not cigaretteItem then
        TriggerClientEvent('QBCore:Notify', src, Config.Messages.no_cigarette, 'error')
        return
    end

    if Config.Items.cigarette.requiresLighter then
        local lighterItem = Player.Functions.GetItemByName(Config.Items.lighter.name)
        if not lighterItem then
            TriggerClientEvent('QBCore:Notify', src, Config.Messages.no_lighter, 'error')
            return
        end
    end

    Player.Functions.RemoveItem(Config.Items.cigarette.name, 1)
    TriggerClientEvent('infc_stressrelief:startSmoking', src)
end)

RegisterNetEvent('QBCore:Server:OnItemAdd', function(item, amount, metadata)
    if item.name == 'cigarette_pack' then
        if not metadata or not metadata.cigarettes then
            local newMetadata = metadata or {}
            newMetadata.cigarettes = Config.Items.cigarette_pack.maxCigarettes
            newMetadata.description = string.format('A pack containing %d cigarettes', Config.Items.cigarette_pack.maxCigarettes)
            item.info = newMetadata
        end
    end
end)

QBCore.Commands.Add(Config.Commands.give_items.name, Config.Commands.give_items.description, {}, false, function(source)
    local Player = QBCore.Functions.GetPlayer(source)
    if Player then

        for _, item in pairs(Config.Commands.give_items.items) do
            if item.name == Config.Items.cigarette_pack.name then

                local packMetadata = {
                    cigarettes = Config.Items.cigarette_pack.maxCigarettes,
                    description = string.format('A pack containing %d cigarettes', Config.Items.cigarette_pack.maxCigarettes)
                }
                Player.Functions.AddItem(item.name, item.amount, false, packMetadata)
            else
                Player.Functions.AddItem(item.name, item.amount)
            end
        end
        TriggerClientEvent('QBCore:Notify', source, Config.Messages.items_received, 'success')
    end
end, Config.Commands.give_items.permission)
